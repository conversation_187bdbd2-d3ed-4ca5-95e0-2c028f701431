// Copyright © 2024-present wzw

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YwAdmin.SqlSugar.Entity.WareHouse;

[SugarTable("Inv_Inbound")]
public class Inbound :BaseEntity
{
    /// <summary>
	/// 入库单号
	/// </summary>
	public string ReceiptNo { get; set; }
	/// <summary>
	/// 入库时间
	/// </summary>
	public DateTime? ReceiptDate { get; set; }
	/// <summary>
	/// 供应商代码
	/// </summary>
	public string SupplierNo { get; set; }
	/// <summary>
	/// 类型
	/// </summary>
	public string InvType { get; set; }
	/// <summary>
	/// 订单号
	/// </summary>
	public string OrderNo { get; set; }
	/// <summary>
	/// 仓库代码
	/// </summary>
	public string WarehouseCode { get; set; }
}
