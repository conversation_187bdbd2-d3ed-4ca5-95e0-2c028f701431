// Copyright © 2024-present wzw

using SqlSugar;
using System;
using System.Linq.Expressions;
using YwAdmin.SqlSugar.Entity;

namespace YwAdmin.Warehouse.Extensions;

/// <summary>
/// SqlSugar 简化扩展方法，让左连接更简单
/// </summary>
public static class SqlSugarSimpleExtensions
{
    /// <summary>
    /// 简化的左连接用户表方法
    /// </summary>
    /// <typeparam name="T">主表类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="userIdSelector">用户ID选择器</param>
    /// <returns>连接查询结果</returns>
    public static ISugarQueryable<T, UserEntity> LeftJoinUser<T>(
        this ISugarQueryable<T> queryable,
        Expression<Func<T, long?>> userIdSelector)
    {
        return queryable.LeftJoin<UserEntity>((main, user) => userIdSelector.Compile()(main) == user.Id);
    }

    /// <summary>
    /// 通用的左连接方法，简化语法
    /// </summary>
    /// <typeparam name="T1">主表类型</typeparam>
    /// <typeparam name="T2">连接表类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="joinCondition">连接条件</param>
    /// <returns>连接查询结果</returns>
    public static ISugarQueryable<T1, T2> Join<T1, T2>(
        this ISugarQueryable<T1> queryable,
        Expression<Func<T1, T2, bool>> joinCondition)
    {
        return queryable.LeftJoin<T2>(joinCondition);
    }

    /// <summary>
    /// 链式条件查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="condition">条件</param>
    /// <param name="predicate">谓词</param>
    /// <returns>查询结果</returns>
    public static ISugarQueryable<T> If<T>(
        this ISugarQueryable<T> queryable,
        bool condition,
        Expression<Func<T, bool>> predicate)
    {
        return queryable.WhereIF(condition, predicate);
    }

    /// <summary>
    /// 链式条件查询（双表）
    /// </summary>
    /// <typeparam name="T1">主表类型</typeparam>
    /// <typeparam name="T2">连接表类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="condition">条件</param>
    /// <param name="predicate">谓词</param>
    /// <returns>查询结果</returns>
    public static ISugarQueryable<T1, T2> If<T1, T2>(
        this ISugarQueryable<T1, T2> queryable,
        bool condition,
        Expression<Func<T1, T2, bool>> predicate)
    {
        return queryable.WhereIF(condition, predicate);
    }

    /// <summary>
    /// 快速分页查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>查询结果</returns>
    public static ISugarQueryable<T> Page<T>(
        this ISugarQueryable<T> queryable,
        int pageIndex,
        int pageSize)
    {
        return queryable.Skip((pageIndex - 1) * pageSize).Take(pageSize);
    }

    /// <summary>
    /// 快速分页查询（双表）
    /// </summary>
    /// <typeparam name="T1">主表类型</typeparam>
    /// <typeparam name="T2">连接表类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>查询结果</returns>
    public static ISugarQueryable<T1, T2> Page<T1, T2>(
        this ISugarQueryable<T1, T2> queryable,
        int pageIndex,
        int pageSize)
    {
        return queryable.Skip((pageIndex - 1) * pageSize).Take(pageSize);
    }
}
