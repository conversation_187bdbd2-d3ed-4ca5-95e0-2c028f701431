<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Localization\Warehouse\en.json" />
    <None Remove="Localization\Warehouse\vi.json" />
    <None Remove="Localization\Warehouse\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\Warehouse\en.json" />
    <EmbeddedResource Include="Localization\Warehouse\vi.json" />
    <EmbeddedResource Include="Localization\Warehouse\zh-Hans.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\YwAdmin.Core\YwAdmin.Core.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex.Contracts\YwAdmin.Multiplex.Contracts.csproj" />
    <ProjectReference Include="..\..\YwAdmin.Multiplex\YwAdmin.Multiplex.csproj" />
    <ProjectReference Include="..\..\YwAdmin.SqlSugar\YwAdmin.SqlSugar.csproj" />
  </ItemGroup>

</Project>
